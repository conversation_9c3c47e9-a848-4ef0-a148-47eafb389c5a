const mongoose = require("mongoose");
const mongoose_delete = require("mongoose-delete");

const feedbackSchema = new mongoose.Schema(
  {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "user" },
    feedBackFor: { type: mongoose.Schema.Types.ObjectId, ref: "user" },
    invitationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "invitation",
    },
    given: {
      type: Boolean,
    },
    status: {
      type: String,
      enum: ["yes", "no"],
    },
  },
  {
    timestamps: true,
  }
);

feedbackSchema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});
const Feedback = mongoose.model("feedback", feedbackSchema);

// Group Feedback Schema for handling group meeting feedback
const groupFeedbackSchema = new mongoose.Schema(
  {
    invitationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "invitation",
      required: true,
      index: true,
    },
    reportedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      required: true,
      index: true,
    },
    allAttended: {
      type: Boolean,
      required: true,
    },
    unAttendeUsers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
      },
    ],
    submittedAt: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Compound index to prevent duplicate feedback from same user for same invitation
groupFeedbackSchema.index({ invitationId: 1, reportedBy: 1 }, { unique: true });

groupFeedbackSchema.plugin(mongoose_delete, {
  overrideMethods: ["find", "findOne", "findOneAndUpdate", "update"],
});

const GroupFeedback = mongoose.model("groupFeedback", groupFeedbackSchema);

module.exports = { Feedback, GroupFeedback };
