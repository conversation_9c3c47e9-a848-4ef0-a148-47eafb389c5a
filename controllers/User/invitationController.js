const mongoose = require("mongoose");
const haversine = require("haversine");
const User = require("../../models/User/User");
const Bundle = require("../../models/User/bundle");
const Invitation = require("../../models/User/invitations");
const Business = require("../../models/Partner/partnerBusiness");
const UserNotification = require("../../models/User/userNotification");
const { sendEmail } = require("../../util/sendEmail");
const { body, validationResult } = require("express-validator");
const { getNotificationText } = require("../../util/getNotificationText");
const { getGoogleNearByPlaces } = require("../../util/getGoogleNearByPlaces");
const {
  getAggregatedInvitaion,
  getAggregatedInvitationsRequests,
} = require("../../util/getAggregatedInvitation");
const { checkUserIsPremium } = require("../../util/checkUserIsPremium");
const Feedback = require("../../models/User/feedback");
const { sendPushNotification } = require("../../util/sendPushNotification");
const Chat = require("../../models/Common/Chat");
const {
  now,
  createUTCDateTime,
  getStartOfDayUTC,
  getEndOfDayUTC,
  getDateRangeUTC,
  addTime,
  subtractTime,
  getTimeDifference,
  isFuture,
  isPast,
  isToday,
  formatForAPI,
  parseTimeString,
} = require("../../util/timeUtils");
//const { default: isEmail } = require("validator/lib/isEmail");

module.exports.getBusinessUnderRadius = async (req, res) => {
  try {
    const { coordinates, category, distance, rating, is_open, price_level } =
      req.body;
    let radius;
    let obj = {};
    if (distance) {
      radius = parseInt(distance) * 1000;
    }
    // Note: These variables are currently unused but kept for potential future business hours filtering
    // const currentDay = now().toLocaleDateString("en-US", { weekday: "short", timeZone: "UTC" }).slice(0, 3);
    // const currentTime = now().toISOString().slice(11, 16); // HH:MM in UTC

    const data = await Business.aggregate([
      {
        $geoNear: {
          near: {
            type: "Point",
            coordinates: coordinates,
          },
          distanceField: "distance",
          maxDistance: Number(radius),
          spherical: true,
        },
      },
      {
        $match: {
          ...obj,
          isPartnerActive: true,
          deleted: false,
          ...(Array.isArray(category) && category.length > 0
            ? { category: { $in: category } }
            : {}),
          ...(rating !== 0 ? { rating: { $gte: Number(rating) } } : {}), // Filter by minimum rating
          ...(price_level !== 0 ? { priceLevel: Number(price_level) } : {}), // Filter by price level
        },
      },
      {
        $project: {
          name: 1,
          category: 1,
          location: 1,
          distance: 1,
          city: 1,
          rating: 1,
          priceLevel: 1,
          taxNumber: 1,
          businessEmail: 1,
          businessMobile: 1,
          businessTel: 1,
          address: 1,
          isPartnerActive: 1,
          partnerId: 1,
          businessSchedule: 1,
          photos: 1,
          // is_open: {
          //   $anyElementTrue: {
          //     $map: {
          //       input: "$businessSchedule",
          //       as: "schedule",
          //       in: {
          //         $and: [
          //           { $eq: ["$$schedule.day", currentDay] }, // Check if today matches schedule day
          //           { $eq: ["$$schedule.status", true] }, // Business should be open (status: true)
          //           {
          //             $lte: ["$$schedule.from", currentTime], // Check if current time is >= from time
          //           },
          //           {
          //             $gte: ["$$schedule.to", currentTime], // Check if current time is <= to time
          //           },
          //         ],
          //       },
          //     },
          //   },
          // },
        },
      },
      // ...(typeof is_open !== "undefined"
      //   ? [
      //     {
      //       $match: {
      //         is_open: is_open === true, // Check if is_open matches the user's filter
      //       },
      //     },
      //   ]
      //   : []),
      {
        $limit: 80,
      },
    ]);
    if (data) {
      let finalData = [];
      if (data.length >= 80) {
        finalData = data;
      } else {
        if (!category) {
          const mapData = await getGoogleNearByPlaces(
            coordinates,
            radius,
            price_level
          );
          finalData = [...data, ...mapData.slice(0, 80 - data.length)];
        } else {
          try {
            const mapData = await getGoogleNearByPlaces(
              coordinates,
              radius,
              category,
              is_open,
              price_level
            );
            finalData = [...data, ...mapData.slice(0, 80 - data.length)];
          } catch (error) {
            if (error.message.includes("quota exceeded")) {
              return res.status(503).json({
                error:
                  "Service temporarily unavailable. Please try again later.",
              });
            } else {
              throw error;
            }
          }
        }
      }

      // sort final data on basis of the distance
      finalData = finalData.sort((a, b) => a.distance - b.distance);

      res.status(200).json({ data: finalData });
    } else throw Error("Data not found");
    //res.status(200).json(data);
  } catch (err) {
    let error = err.message;
    res.status(400).json({ error: error });
  }
};

module.exports.getAllPlaces = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    let obj = {};
    let { _id } = req.query;
    if (_id) {
      obj["_id"] = req.query;
    }
    const data = await Business.find({
      deleted: false,
      ...obj,
      isPartnerActive: true,
    })
      .populate("partnerId")
      .skip(skipValue)
      .limit(limit)
      .sort({ createdAt: -1 });

    let count = await Business.find({
      deleted: false,
      isPartnerActive: true,
      ...obj,
    }).countDocuments();

    res.status(200).json({
      data: _id ? (data && data.length > 0 ? data[0] : false) : data,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

const checkIsDailyInviteFinish = async (userId) => {
  const { startOfDay, endOfDay } = getDateRangeUTC(now());
  const invitationCount = await Invitation.countDocuments({
    invitationBy: userId,
    createdAt: { $gte: startOfDay, $lt: endOfDay },
  });
  await User.updateOne(
    { _id: userId },
    { $set: { dailyInviteCount: invitationCount } }
  );
  return invitationCount;
};

// Helper functions for better organization
class InvitationValidator {
  constructor(invitationBy, users, date, time) {
    this.invitationBy = invitationBy;
    this.users = users;
    this.date = date;
    this.time = time;
    this.isGroupInvite = users.length > 1;
    this.invitationDateTime = createUTCDateTime(date, time);
    const dateRange = getDateRangeUTC(date);
    this.startOfDay = dateRange.startOfDay;
    this.endOfDay = dateRange.endOfDay;
  }

  // Check if user has cover image
  async validateUserCoverImage() {
    const user = await User.findById(this.invitationBy);
    if (!user.coverImage) {
      throw new ValidationError(
        "You must have a cover image to send an invitation."
      );
    }
    return user;
  }

  // Validate date is within 7 days
  validateDateRange() {
    const currentDate = now();
    const sevenDaysFromNow = addTime(currentDate, 7, "days");

    if (this.invitationDateTime > sevenDaysFromNow) {
      throw new ValidationError(
        "You can only schedule meetings up to 7 days in advance. Please select an earlier date."
      );
    }
  }

  // Check for blocked period conflicts (3 hours before/after existing invitations)
  // async validateBlockedPeriods() {
  //   const blockedPeriodCheck = await checkForBlockedPeriodConflicts(
  //     this.invitationBy,
  //     this.invitationDateTime
  //   );

  //   if (blockedPeriodCheck.hasConflict) {
  //     const conflictTime =
  //       blockedPeriodCheck.conflictingDateTime.toLocaleTimeString([], {
  //         hour: "2-digit",
  //         minute: "2-digit",
  //         hour12: false,
  //       });
  //     const conflictDate =
  //       blockedPeriodCheck.conflictingDateTime.toLocaleDateString();

  //     throw new ConflictError(
  //       `You cannot schedule a meetup at this time because it conflicts with the blocked period of your existing invitation with ${blockedPeriodCheck.conflictingUserName} on ${conflictDate} at ${blockedPeriodCheck.conflictingTime}. Please select a time that is at least 3 hours before or after your existing invitation.`,
  //       "BLOCKED_PERIOD_CONFLICT"
  //     );
  //   }
  // }
  async validateBlockedPeriods() {
    // Get target user ID for 1-on-1 invitations
    const targetUserId =
      !this.isGroupInvite && this.users.length > 0
        ? this.users[0].userId
        : null;

    const blockedPeriodCheck = await checkForBlockedPeriodConflicts(
      this.invitationBy,
      this.invitationDateTime,
      targetUserId
    );

    if (blockedPeriodCheck.hasConflict) {
      if (
        blockedPeriodCheck.conflictType === "EXISTING_INVITATION_BETWEEN_USERS"
      ) {
        // Throw existing invitation error
        throw new ConflictError(
          blockedPeriodCheck.errorMessage,
          "EXISTING_INVITATION_BETWEEN_USERS",
          {
            existingInvitationId: blockedPeriodCheck.existingInvitationId,
          }
        );
      } else {
        // Throw blocked period error
        const conflictTime =
          blockedPeriodCheck.conflictingDateTime.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });
        const conflictDate =
          blockedPeriodCheck.conflictingDateTime.toLocaleDateString();

        throw new ConflictError(
          `You cannot schedule a meetup at this time because it conflicts with the blocked period of your existing invitation with ${blockedPeriodCheck.conflictingUserName} on ${conflictDate} at ${blockedPeriodCheck.conflictingTime}. Please select a time that is at least 3 hours before or after your existing invitation.`,
          "BLOCKED_PERIOD_CONFLICT"
        );
      }
    }
  }
  // Check for existing invitations between specific users (only for 1-on-1)
  async validateExistingUserInvitations() {
    const nowDate = now();
    console.log("nowDate", nowDate);

    if (this.isGroupInvite) return; // Skip for group invitations
    console.log("this.isGroupInvite", this.isGroupInvite);

    const invitee = this.users[0];
    // Check for active invitation between these two specific users
    const existingInvitation = await Invitation.findOne({
      $or: [
        {
          invitationBy: this.invitationBy,
          "users.userId": invitee.userId,
          status: { $in: ["Pending", "Accepted"] },
          date: { $gte: nowDate },
          isGroup: { $ne: true },
          deleted: { $ne: true },
        },
        {
          invitationBy: invitee.userId,
          "users.userId": this.invitationBy,
          status: { $in: ["Pending", "Accepted"] },
          date: { $gte: nowDate },
          isGroup: { $ne: true },
          deleted: { $ne: true },
        },
      ],
      isGroup: this.isGroupInvite,
    })
      .populate("feedbackId")
      .populate("invitationBy", "userName");

    if (existingInvitation) {
      const isCompleted = existingInvitation.feedbackId?.given;
      if (!isCompleted) {
        const inviteeName = await User.findById(invitee.userId).select(
          "userName"
        );
        const isCurrentUserSender =
          existingInvitation.invitationBy._id.toString() ===
          this.invitationBy.toString();

        const errorMessage = isCurrentUserSender
          ? `You already have an active invitation with ${inviteeName.userName}. Please wait for them to respond or cancel the existing invitation.`
          : `${inviteeName.userName} has already sent you an invitation. Please respond to their invitation first.`;

        throw new ConflictError(
          errorMessage,
          "EXISTING_INVITATION_BETWEEN_USERS",
          {
            existingInvitationId: existingInvitation._id,
          }
        );
      }
    }
  }

  // Check sender's time conflicts
  async validateSenderTimeConflicts() {
    const [sentInvitations, receivedInvitations] = await Promise.all([
      this.getActiveSentInvitations(),
      this.getActiveReceivedInvitations(),
    ]);

    const allUserInvitations = [...sentInvitations, ...receivedInvitations];

    for (const invitation of allUserInvitations) {
      const conflict = this.checkTimeConflict(invitation);
      if (conflict.hasConflict) {
        const message = this.isGroupInvite
          ? "You have a meeting scheduled at this exact time. Please choose a different time."
          : "You have already sent an invitation or scheduled a meeting within 3 hours of this time. Please choose a different time.";

        throw new ConflictError(message, "SENDER_TIME_CONFLICT");
      }
    }
  }

  // // Check for group invitation conflicts (only same day/time)
  // async validateGroupInvitationConflicts() {
  //   if (!this.isGroupInvite) return;

  //   const conflictingInvitations = await Invitation.find({
  //     $or: [
  //       {
  //         invitationBy: this.invitationBy,
  //         "users.userId": { $in: this.users.map((u) => u.userId) },
  //         "users.status": "Accepted",
  //         deleted: { $ne: true },
  //         // date: { $gte: this.startOfDay, $lte: this.endOfDay },
  //       },
  //       {
  //         invitationBy: { $in: this.users.map((u) => u.userId) },
  //         "users.userId": this.invitationBy,
  //         "users.status": "Accepted",
  //         deleted: { $ne: true },
  //         // date: { $gte: this.startOfDay, $lte: this.endOfDay },
  //       },
  //     ],
  //   });
  //   console.log("conflictingInvitations", conflictingInvitations);
  //   // Check for time conflicts on the same day
  //   const hasTimeConflict = conflictingInvitations.some((inv) => {
  //     const existingTime = new Date(inv.date);
  //     const timeDiff =
  //       Math.abs(this.invitationDateTime - existingTime) / (1000 * 60 * 60);
  //     return timeDiff < 0.5; // 30-minute buffer for group invitations
  //   });

  //   if (hasTimeConflict) {
  //     throw new ConflictError(
  //       `You have a scheduling conflict with existing invitations on ${this.date}. Please choose a different time.`,
  //       "GROUP_TIME_CONFLICT"
  //     );
  //   }
  // }
  // Check for group invitation conflicts and existing invitations with same users
  async validateGroupInvitationConflicts() {
    if (!this.isGroupInvite) return;

    const currentTime = now();

    const conflictingInvitations = await Invitation.find({
      $or: [
        {
          invitationBy: this.invitationBy,
          "users.userId": { $in: this.users.map((u) => u.userId) },
          status: { $in: ["Pending", "Accepted"] }, // Check both pending and accepted
          deleted: { $ne: true },
          isGroup: true,
          date: { $gte: currentTime }, // Only future dates (past dates are expired)
        },
        {
          invitationBy: { $in: this.users.map((u) => u.userId) },
          "users.userId": this.invitationBy,
          status: { $in: ["Pending", "Accepted"] }, // Check both pending and accepted
          deleted: { $ne: true },
          isGroup: true,
          date: { $gte: currentTime }, // Only future dates (past dates are expired)
        },
      ],
    }).populate("feedbackId");

    // Filter out completed invitations (those with feedback given)
    const activeInvitations = conflictingInvitations.filter((inv) => {
      return !inv.feedbackId?.given;
    });

    console.log("activeInvitations", activeInvitations);

    if (activeInvitations.length > 0) {
      // Check for existing invitations with same users (like EXISTING_INVITATION_BETWEEN_USERS)
      const duplicateUsers = new Set();

      for (const inv of activeInvitations) {
        // Check if any users from existing invitation overlap with new invitation users
        for (const existingUser of inv.users) {
          const existingUserIdStr = existingUser.userId.toString();

          // Check if this user is in our new invitation list
          const isDuplicate = this.users.some((user) => {
            const newUserIdStr = user.userId.toString();
            return newUserIdStr === existingUserIdStr;
          });

          if (isDuplicate) {
            duplicateUsers.add(existingUserIdStr);
          }
        }

        // Also check if the invitation sender is in our new users list
        const invitationByStr = inv.invitationBy.toString();
        const isSenderDuplicate = this.users.some((user) => {
          return user.userId.toString() === invitationByStr;
        });

        if (isSenderDuplicate) {
          duplicateUsers.add(invitationByStr);
        }
      }

      if (duplicateUsers.size > 0) {
        // Get usernames for the error message
        const duplicateUserIds = Array.from(duplicateUsers).map((id) =>
          mongoose.Types.ObjectId(id)
        );

        const duplicateUserNames = await User.find({
          _id: { $in: duplicateUserIds },
        }).select("userName");

        throw new ConflictError(
          `You already have active invitations with: ${duplicateUserNames
            .map((u) => u.userName)
            .join(
              ", "
            )}. Please wait until the current invitation ends or is completed.`,
          "EXISTING_GROUP_INVITATION_BETWEEN_USERS"
        );
      }

      // If no user duplicates, check for time conflicts on the same day
      const sameDayInvitations = activeInvitations.filter((inv) => {
        const existingDate = new Date(inv.date);
        const existingDateRange = getDateRangeUTC(existingDate);

        return (
          this.invitationDateTime >= existingDateRange.startOfDay &&
          this.invitationDateTime <= existingDateRange.endOfDay
        );
      });

      const hasTimeConflict = sameDayInvitations.some((inv) => {
        const existingTime = new Date(inv.date);
        const timeDiffHours = getTimeDifference(
          this.invitationDateTime,
          existingTime,
          "hours"
        );
        return timeDiffHours < 0.5; // 30-minute buffer for group invitations
      });

      if (hasTimeConflict) {
        throw new ConflictError(
          `You have a scheduling conflict with existing invitations on ${this.date}. Please choose a different time.`,
          "GROUP_TIME_CONFLICT"
        );
      }
    }
  }
  // Check invitee availability
  // async validateInviteeAvailability() {
  //   for (const invitee of this.users) {
  //     const activeInvitations = await this.getActiveInvitationsForUser(
  //       invitee.userId
  //     );

  //     for (const existing of activeInvitations) {
  //       const conflict = this.checkTimeConflict(existing);
  //       if (conflict.hasConflict) {
  //         const inviteeUser = await User.findById(invitee.userId);
  //         const conflictThreshold = this.isGroupInvite ? 0.5 : 3;
  //         const timeWindow = this.isGroupInvite ? "30 minutes" : "3 hours";

  //         throw new ConflictError(
  //           `User ${inviteeUser.userName} has an existing invitation within ${timeWindow} of your requested time. Please choose a different time.`,
  //           "INVITEE_CONFLICT"
  //         );
  //       }
  //     }
  //   }
  // }

  // Helper methods
  async getActiveSentInvitations() {
    const invitations = await Invitation.find({
      invitationBy: this.invitationBy,
      date: { $gte: this.startOfDay, $lt: this.endOfDay },
      status: { $in: ["Pending", "Accepted"] },
      deleted: { $ne: true },
    }).populate("feedbackId");

    return invitations.filter((inv) => !inv.feedbackId?.given);
  }

  async getActiveReceivedInvitations() {
    const invitations = await Invitation.find({
      "users.userId": this.invitationBy,
      "users.status": "Accepted",
      date: { $gte: this.startOfDay, $lt: this.endOfDay },
      deleted: { $ne: true },
    }).populate("feedbackId");

    return invitations.filter((inv) => !inv.feedbackId?.given);
  }

  async getActiveInvitationsForUser(userId) {
    const invitations = await Invitation.find({
      "users.userId": userId,
      date: { $gte: this.startOfDay, $lt: this.endOfDay },
      status: { $in: ["Pending", "Accepted"] },
      deleted: { $ne: true },
    }).populate("feedbackId");

    return invitations.filter((inv) => !inv.feedbackId?.given);
  }

  checkTimeConflict(invitation) {
    const existingTimeParts = parseTimeString(invitation.time);
    if (!existingTimeParts) return { hasConflict: false };

    const existingDateTime = createUTCDateTime(
      invitation.date.toISOString().split("T")[0],
      `${existingTimeParts[0]
        .toString()
        .padStart(2, "0")}:${existingTimeParts[1].toString().padStart(2, "0")}`
    );

    const diffInHours = getTimeDifference(
      this.invitationDateTime,
      existingDateTime,
      "hours"
    );

    // Different conflict thresholds based on invitation type
    const conflictThreshold = this.isGroupInvite ? 0.5 : 3; // 30 min for group, 3 hours for individual

    return {
      hasConflict: diffInHours < conflictThreshold,
      timeDifference: diffInHours,
    };
  }
}

// Custom error classes
class ValidationError extends Error {
  constructor(message, case_type = "VALIDATION_ERROR") {
    super(message);
    this.name = "ValidationError";
    this.statusCode = 400;
    this.case = case_type;
    this.additionalData = {};
  }
}

class ConflictError extends Error {
  constructor(message, case_type, additionalData = {}) {
    super(message);
    this.name = "ConflictError";
    this.statusCode = 409;
    this.case = case_type;
    this.additionalData = additionalData;
  }
}

// Main invitation handler
module.exports.inviteUser = [
  // Enhanced validation
  body("users")
    .isArray({ min: 1 })
    .withMessage("At least one user is required"),
  body("date").isISO8601().withMessage("Valid date is required"),
  body("time")
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Valid time format (HH:MM) is required"),

  async (req, res) => {
    try {
      // Basic validation
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { users, date, time, confirmCase2B, invitationId, note } = req.body;
      const invitationBy = req.user._id;

      // Handle confirmation case
      if (confirmCase2B) {
        await Invitation.findByIdAndUpdate(invitationId, {
          status: "Rejected",
          deleted: true,
        });
      }

      // Validate time format
      const timeParts = parseTimeString(time);
      if (!timeParts) {
        return res.status(400).json({
          error: "Invalid time format. Please use HH:MM format.",
        });
      }

      const invitationDateTime = createUTCDateTime(date, time);
      if (isNaN(invitationDateTime.getTime())) {
        return res.status(400).json({
          error: "Invalid date/time combination.",
        });
      }

      // Initialize validator
      const validator = new InvitationValidator(
        invitationBy,
        users,
        date,
        time
      );

      // Run all validations
      const user = await validator.validateUserCoverImage();
      // Check subscription limits
      const limits = await validateSubscriptionLimits(invitationBy);
      validator.validateDateRange();
      await validator.validateBlockedPeriods();
      await validator.validateExistingUserInvitations(); // Only for 1-on-1
      await validator.validateSenderTimeConflicts();
      await validator.validateGroupInvitationConflicts(); // Only for group
      // await validator.validateInviteeAvailability();

      // Create the invitation
      const invitation = await createInvitation({
        ...req.body,
        date: invitationDateTime,
        invitationBy,
        isGroup: users.length > 1,
        note,
        limits,
      });

      // Send notifications
      await sendInvitationNotifications(user, users, invitation);

      res.status(201).json({ invitePeople: invitation });
    } catch (error) {
      console.error("Error in inviteUser:", error);

      if (error instanceof ValidationError || error instanceof ConflictError) {
        return res.status(error.statusCode).json({
          error: error.message,
          case: error.case,
          ...error.additionalData,
        });
      }

      res.status(500).json({
        error: "Failed to process invitation",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      });
    }
  },
];

// Helper functions
async function validateSubscriptionLimits(userId) {
  const isPremiumUser = await checkUserIsPremium(userId);
  let isFreeInvitation = false;
  let isUserHaveBundle = false;
  console.log("isPremiumUser", isPremiumUser);
  if (!isPremiumUser) {
    const dailyInviteCount = await checkIsDailyInviteFinish(userId);
    console.log("dailyInviteCount", dailyInviteCount);
    if (dailyInviteCount < 2) {
      isFreeInvitation = true;
    } else {
      const findBundle = await Bundle.findOne({ userId });
      if (findBundle?.pendingInvitation > 0) {
        isUserHaveBundle = true;
      }
    }
  }
  console.log("isFreeInvitation", isFreeInvitation);
  if (!isFreeInvitation && !isPremiumUser && !isUserHaveBundle) {
    throw new ValidationError(
      "Please purchase a subscription or any plan to invite more people"
    );
  }

  return { isFreeInvitation, isPremiumUser, isUserHaveBundle };
}

async function createInvitation(invitationData) {
  const { limits } = invitationData;
  const feedback = await Feedback.create({
    userId: invitationData.invitationBy,
    given: false,
    status: "no",
  });

  const invitation = await Invitation.create({
    ...invitationData,
    feedbackId: feedback._id,
  });
  console.log("limits", limits);
  // Update bundle if used
  // const limits = await validateSubscriptionLimits(invitationData.invitationBy);
  if (limits.isUserHaveBundle) {
    await Bundle.findOneAndUpdate(
      { userId: invitationData.invitationBy },
      { $inc: { pendingInvitation: -1 } }
    );
  }

  return invitation;
}

async function sendInvitationNotifications(sender, users, invitation) {
  await Promise.all(
    users.map(async (invitee) => {
      // Create notification
      await UserNotification.create({
        title: sender.userName,
        body: getNotificationText()["Sent_Invitation"],
        image: sender.coverImage,
        userId: invitee.userId,
      });

      const inviteeUser = await User.findById(invitee.userId);

      // Send push notification
      // if (inviteeUser?.fcmToken) {
      //   await sendPushNotification({
      //     title: `from ${sender.userName}`,
      //     body: `Hello ${inviteeUser.userName}, you have received an invitation.`,
      //     fcmToken: inviteeUser.fcmToken,
      //     userType: "USER",
      //   });
      // }

      // Send email
      await sendEmail({
        from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
        to: inviteeUser.email,
        subject: "Invitation Notification",
        text: `Hey, ${sender.userName} has sent you an invitation`,
      });
    })
  );
}

module.exports.getMyInvitations = async (req, res) => {
  try {
    const userId = req.user._id;

    const invitations = await Invitation.aggregate([
      {
        $match: {
          deleted: false,
          invitationBy: new mongoose.Types.ObjectId(userId),
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "rescheduleBy",
          foreignField: "_id",
          as: "rescheduleBy",
        },
      },
      { $unwind: { path: "$rescheduleBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "feedbacks",
          localField: "feedbackId",
          foreignField: "_id",
          as: "feedbackId",
        },
      },
      { $unwind: { path: "$feedbackId", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "invitationBy",
          foreignField: "_id",
          as: "invitationBy",
        },
      },
      { $unwind: { path: "$invitationBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "partnerbusinesses",
          localField: "businessId",
          foreignField: "_id",
          as: "businessData",
        },
      },
      { $unwind: { path: "$businessData", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$users", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "users.userId",
          foreignField: "_id",
          as: "users.userId",
        },
      },
      { $unwind: { path: "$users.userId", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          businessId: {
            $ifNull: ["$businessData", "$googleBusiness"],
          },
        },
      },
      {
        $group: {
          _id: "$_id",
          users: { $push: "$users" },
          groupName: { $first: "$groupName" },
          dob: { $first: "$dob" },
          isSeen: { $first: "$isSeen" },
          invitationBy: { $first: "$invitationBy" },
          feedbackId: { $first: "$feedbackId" },
          businessId: { $first: "$businessId" },
          date: { $first: "$date" },
          time: { $first: "$time" },
          isGroup: { $first: "$isGroup" },
          isRescheduled: { $first: "$isRescheduled" },
          rescheduleBy: { $first: "$rescheduleBy" },
          status: { $first: "$status" },
          reschedule: { $first: "$reschedule" },
          createdAt: { $first: "$createdAt" },
          isPremium: { $first: "$invitationBy.isPremium" },
        },
      },
      {
        $addFields: {
          isPremiumRecentlySent: {
            $and: [
              { $eq: ["$isPremium", true] },
              { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
              {
                $lt: [
                  { $subtract: [new Date(), "$createdAt"] },
                  24 * 60 * 60 * 1000,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          isPremiumRecentlySent: -1,
          createdAt: -1,
        },
      },
    ]);

    if (!invitations || invitations.length === 0) {
      return res.status(404).json({ error: "No invitations found." });
    }

    res
      .status(200)
      .json({ invitation: invitations, total: invitations.length });
  } catch (err) {
    console.error("getMyInvitations error:", err);
    res.status(400).json({ error: "Something went wrong" });
  }
};

module.exports.getInvitations = async (req, res) => {
  try {
    const userId = new mongoose.Types.ObjectId(req.user._id);

    // Common stages used in both pipelines
    const commonLookups = [
      {
        $lookup: {
          from: "users",
          localField: "rescheduleBy",
          foreignField: "_id",
          as: "rescheduleBy",
        },
      },
      { $unwind: { path: "$rescheduleBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "feedbacks",
          localField: "feedbackId",
          foreignField: "_id",
          as: "feedbackId",
        },
      },
      { $unwind: { path: "$feedbackId", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "invitationBy",
          foreignField: "_id",
          as: "invitationBy",
        },
      },
      { $unwind: { path: "$invitationBy", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "partnerbusinesses",
          localField: "businessId",
          foreignField: "_id",
          as: "businessData",
        },
      },
      { $unwind: { path: "$businessData", preserveNullAndEmptyArrays: true } },
      { $unwind: { path: "$users", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: "users",
          localField: "users.userId",
          foreignField: "_id",
          as: "users.userId",
        },
      },
      { $unwind: { path: "$users.userId", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          businessId: {
            $ifNull: ["$businessData", "$googleBusiness"],
          },
        },
      },
      {
        $group: {
          _id: "$_id",
          users: { $push: "$users" },
          groupName: { $first: "$groupName" },
          dob: { $first: "$dob" },
          isSeen: { $first: "$isSeen" },
          invitationBy: { $first: "$invitationBy" },
          feedbackId: { $first: "$feedbackId" },
          businessId: { $first: "$businessId" },
          date: { $first: "$date" },
          time: { $first: "$time" },
          isGroup: { $first: "$isGroup" },
          isRescheduled: { $first: "$isRescheduled" },
          rescheduleBy: { $first: "$rescheduleBy" },
          status: { $first: "$status" },
          reschedule: { $first: "$reschedule" },
          createdAt: { $first: "$createdAt" },
          isPremium: { $first: "$invitationBy.isPremium" },
        },
      },
      {
        $addFields: {
          isPremiumRecentlySent: {
            $and: [
              { $eq: ["$isPremium", true] },
              { $gte: [{ $subtract: [new Date(), "$createdAt"] }, 0] },
              {
                $lt: [
                  { $subtract: [new Date(), "$createdAt"] },
                  24 * 60 * 60 * 1000,
                ],
              },
            ],
          },
        },
      },
      {
        $sort: {
          isPremiumRecentlySent: -1,
          createdAt: -1,
        },
      },
    ];

    // INVITATIONS:
    // 1. Invitations sent to me by others (I'm recipient)
    // 2. My own sent invitations
    const invitationsPipeline = [
      {
        $match: {
          deleted: false,
          $or: [
            // I'm a recipient (invitation was sent to me)
            {
              "users.userId": userId,
              invitationBy: { $ne: userId },
            },
            // I'm the sender (invitation was sent by me)
            {
              invitationBy: userId,
            },
          ],
        },
      },
      ...commonLookups,
    ];

    // OPEN REQUESTS:
    // 1. Invitations I rescheduled (originally sent to me)
    // 2. Invitations I sent that were rescheduled by recipient
    const openRequestsPipeline = [
      {
        $match: {
          deleted: false,
          $or: [
            // Invitations originally sent to me that I rescheduled
            {
              "users.userId": userId,
              isRescheduled: true,
              rescheduleBy: userId,
            },
            // Invitations I sent that were rescheduled by recipient
            {
              invitationBy: userId,
              isRescheduled: true,
              rescheduleBy: { $ne: userId },
            },
          ],
        },
      },
      ...commonLookups,
    ];

    const [invitations, openRequests] = await Promise.all([
      Invitation.aggregate(invitationsPipeline),
      Invitation.aggregate(openRequestsPipeline),
    ]);

    res.status(200).json({
      invitations,
      openRequests,
      total: invitations.length + openRequests.length,
    });
  } catch (err) {
    console.error("getInvitations error:", err);
    res.status(400).json({ error: "Something went wrong" });
  }
};

module.exports.getAllRequests = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    const currentDate = now();

    // Start of today in UTC
    const startOfDay = getStartOfDayUTC(currentDate);
    let obj = {};

    let aggObj = {
      date: { $gte: startOfDay },
    };

    if (req.query.status) {
      obj["status"] = req.query.status;
      aggObj["status"] = req.query.status;
    }
    if (req.query.myInvitations === "true") {
      const userConditions = [
        // { "invitationBy._id": mongoose.Types.ObjectId(req.user._id) },
        { "rescheduleBy._id": mongoose.Types.ObjectId(req.user._id) },
        // { movedToOpenRequests: true },
      ];

      obj["$or"] = userConditions;
      obj["status"] = "Pending";
      obj["status"] = "Pending";

      aggObj["$or"] = userConditions;
      aggObj["status"] = "Pending";
    }
    // if (req.query.myInvitations === "true") {
    //   obj["invitationBy"] = req.user._id;
    //   obj["status"] = "Pending";
    //   aggObj["status"] = "Pending";
    //   aggObj["invitationBy._id"] = mongoose.Types.ObjectId(req.user._id);
    // }

    const data = await getAggregatedInvitaion({ skipValue, limit, aggObj });
    if (!data || !Array.isArray(data)) {
      return res.status(200).json({
        futureInvitations: [],
        totalData: 0,
        totalPage: 0,
        perPage: limit,
        currentPage: page,
      });
    }
    const currentUser = await User.findById(req.user?._id).select("location");
    const futureInvitations = data.filter((ir) => {
      try {
        // Add distance to each user in the users array
        const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

        // Helper function to calculate distance between two coordinates
        const calculateDistance = (userCoords) => {
          const currentUserLatLon = {
            latitude: currentUserCoords[0], // Latitude
            longitude: currentUserCoords[1], // Longitude
          };

          const userLatLon = {
            latitude: userCoords[0] || 0, // Latitude
            longitude: userCoords[1] || 0, // Longitude
          };

          const distance = haversine(currentUserLatLon, userLatLon, {
            unit: "km",
          });

          return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
        };
        const usersWithDistance = ir.users.map((user) => {
          const userCoords = user.userId.location?.coordinates || [0, 0];
          //console.log("userCoords", userCoords);
          const distance = calculateDistance(userCoords);
          //console.log("user", user);
          return {
            ...user,
            userId: {
              ...user.userId,
              distance,
            },
          };
        });
        //console.log("usersWithDistance", usersWithDistance);
        // Calculate distance for invitation creator
        const invitationByCoords = ir.invitationBy.location?.coordinates || [
          0, 0,
        ];
        const invitationByDistance = calculateDistance(invitationByCoords);
        // Create invitationBy user object to add to users array
        const invitationByUser = {
          userId: {
            ...ir.invitationBy,
            distance: invitationByDistance,
          },
          status: "Pending",
          _id: ir.invitationBy._id.toString(), // Generate a unique ID based on the invitationBy user's ID
        };

        // Convert invitation to a plain object and modify it
        const responseData = ir;
        if (responseData.isGroup) {
          // If isGroup is true, add the invitationBy user to the users array
          responseData.users = [...usersWithDistance, invitationByUser];
        }
        // If isGroup is false, just return the users with distance
        else {
          responseData.users = usersWithDistance;
        }

        // Ensure that 'ir.time' exists
        if (!ir.time) {
          throw new Error("Missing time value");
        }

        // Use the parseTimeString function to handle different time formats
        const timeParts = parseTimeString(ir.time);
        if (!timeParts) {
          throw new Error("Invalid time format");
        }

        const invitationDateTime = createUTCDateTime(
          ir.date.toISOString().split("T")[0],
          `${timeParts[0].toString().padStart(2, "0")}:${timeParts[1]
            .toString()
            .padStart(2, "0")}`
        );

        // Check if the invitation date is today
        if (isToday(invitationDateTime)) {
          // If today, check the time - only return future times
          return isFuture(invitationDateTime);
        } else {
          // Otherwise, for future dates, simply check if the invitation date is in the future
          return isFuture(invitationDateTime);
        }
      } catch (error) {
        console.error(
          `Error parsing date or time for invitation: ${ir.date} ${ir.time}`,
          error
        );
        return false; // Skip invalid entries
      }
    });

    // Filter out past invitations
    // const futureInvitations = data.filter((ir) => {
    //   try {
    //     const invitationTime = ir.time.split("(")[1].split(")")[0]; // Extract time from 'TimeOfDay(HH:MM)' format
    //     const invitationDateTime = new Date(ir.date);

    //     console.log("invitationDate before set", invitationDateTime);
    //     const [hours, minutes] = invitationTime.split(":").map(Number);
    //     invitationDateTime.setHours(hours, minutes, 0, 0); // Set hours and minutes
    //     // Check if the invitation date is today
    //     if (invitationDateTime.toDateString() === currentDate.toDateString()) {
    //       // If today, check the time
    //       const currentTimeDate = parseTimeOfDay(currentTime);
    //       return invitationDateTime > currentTimeDate; // Only future times for today's date
    //     } else {
    //       // Otherwise, for future dates, simply check if the invitation date is in the future
    //       return invitationDateTime > currentDate;
    //     }
    //   } catch (error) {
    //     console.error(
    //       `Error parsing date or time for invitation: ${ir.date} ${ir.time}`,
    //       error
    //     );
    //     return false; // Skip invalid entries
    //   }
    // });

    let count = await Invitation.find({
      deleted: false,
      ...obj,
    }).countDocuments();

    res.status(200).json({
      data: futureInvitations,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

module.exports.getInvitationsRequests = async (req, res) => {
  try {
    let page = parseInt(req.query.page ? req.query.page : 1);
    let limit = parseInt(req.query.limit ? req.query.limit : 100);
    let skipValue = (page - 1) * limit;
    const { status, userId } = req.query;
    let obj = {};
    let aggObj = {};

    if (status) {
      obj["status"] = status;
      aggObj["status"] = status;
    }
    if (userId) {
      obj["users"] = {
        $elemMatch: { userId },
      };
      aggObj["users.userId._id"] = mongoose.Types.ObjectId(userId);
    }

    // Fetch the current logged-in user's location
    const currentUser = await User.findById(userId).select("location");
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    const data = await getAggregatedInvitationsRequests({
      skipValue,
      limit,
      aggObj,
      userId,
    });
    let count = await Invitation.find({
      deleted: false,
      ...obj,
    }).countDocuments();

    // Add distance to each user in the users array
    const responseData = data.map((invitation) => {
      const usersWithDistance = invitation.users.map((user) => {
        const userCoords = user.userId.location?.coordinates || [0, 0]; // Default to [0, 0] if no location
        // Ensure coordinates are in [latitude, longitude] order for haversine
        const currentUserLatLon = {
          latitude: currentUserCoords[0], // Latitude
          longitude: currentUserCoords[1], // Longitude
        };
        const userLatLon = {
          latitude: userCoords[0], // Latitude
          longitude: userCoords[1], // Longitude
        };
        const distance = haversine(currentUserLatLon, userLatLon, {
          unit: "km",
        }); // Distance in kilometers

        return {
          ...user,
          userId: {
            ...user.userId,
            distance: parseFloat(distance.toFixed(2)), // Round to 2 decimal places
          },
        };
      });
      if (invitation.isGroup) {
        const inviteUserLatLon = {
          latitude: invitation.invitationBy.location?.coordinates[0], // Latitude
          longitude: invitation.invitationBy.location?.coordinates[1], // Longitude
        };
        const currentUserLatLon = {
          latitude: currentUserCoords[0], // Latitude
          longitude: currentUserCoords[1], // Longitude
        };
        const inviteUserDistance = haversine(
          currentUserLatLon,
          inviteUserLatLon,
          {
            unit: "km",
          }
        );
        // If isGroup is true, add invitationBy to the users array
        // Note: We're not using the filtered users here, just adding the invitationBy to all users
        return {
          ...invitation,
          users: [
            ...usersWithDistance,
            {
              userId: {
                ...invitation.invitationBy,
                distance: inviteUserDistance, // Distance from the current user to themselves is 0
              },
              status: "Pending",
              _id: "6787b2d03fc440ddf57a8f4f", // Add a unique ID for the invitationBy user
            },
          ],
        };
      } else {
        // If isGroup is false, return the invitation with updated users array (including distance)
        return {
          ...invitation,
          users: usersWithDistance,
        };
      }
    });

    res.status(200).json({
      data: responseData,
      totalData: count,
      totalPage: Math.ceil(count / limit),
      perPage: limit,
      currentPage: page,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

module.exports.getInvitationById = async (req, res) => {
  try {
    const { _id, userId } = req.query;

    if (!_id) {
      return res.status(400).json({ error: "Invitation ID is required" });
    }

    // Fetch the invitation with all populated fields
    const invitation = await Invitation.findById({ _id })
      .populate("rescheduleBy")
      .populate("feedbackId")
      .populate("invitationBy")
      .populate("businessId")
      .populate("users.userId");

    if (!invitation) {
      return res.status(404).json({ error: "Invitation not found" });
    }

    // Fetch the current user's location for distance calculation
    const currentUser = await User.findById(userId || req.user?._id).select(
      "location"
    );
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    // Helper function to calculate distance between two coordinates
    const calculateDistance = (userCoords) => {
      const currentUserLatLon = {
        latitude: currentUserCoords[0], // Latitude
        longitude: currentUserCoords[1], // Longitude
      };

      const userLatLon = {
        latitude: userCoords[0] || 0, // Latitude
        longitude: userCoords[1] || 0, // Longitude
      };

      const distance = haversine(currentUserLatLon, userLatLon, {
        unit: "km",
      });

      return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
    };

    // Add distance to each user in the users array
    const usersWithDistance = invitation.users.map((user) => {
      const userCoords = user.userId.location?.coordinates || [0, 0];
      const distance = calculateDistance(userCoords);

      return {
        ...user.toObject(),
        userId: {
          ...user.userId.toObject(),
          distance,
        },
      };
    });

    // Calculate distance for invitation creator
    const invitationByCoords = invitation.invitationBy.location
      ?.coordinates || [0, 0];
    const invitationByDistance = calculateDistance(invitationByCoords);

    // Create invitationBy user object to add to users array
    const invitationByUser = {
      userId: {
        ...invitation.invitationBy.toObject(),
        distance: invitationByDistance,
      },
      status: "Pending",
      _id: invitation.invitationBy._id.toString(), // Generate a unique ID based on the invitationBy user's ID
    };

    // Convert invitation to a plain object and modify it
    const responseData = invitation.toObject();
    if (responseData.isGroup) {
      // If isGroup is true, add the invitationBy user to the users array
      responseData.users = [...usersWithDistance, invitationByUser];
    }
    // If isGroup is false, just return the users with distance
    else {
      responseData.users = usersWithDistance;
    }
    // responseData.users = [...usersWithDistance, invitationByUser];

    res.status(200).json({
      data: responseData,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};
module.exports.getInvitationByLastAccepted = async (req, res) => {
  try {
    const { user1Id, user2Id } = req.query;

    // Check if both user IDs are provided
    if (!user1Id || !user2Id) {
      return res.status(400).json({ error: "Both user IDs are required" });
    }

    // Find the latest invitation where both users are involved and at least one has accepted
    const latestInvitation = await Invitation.findOne({
      $or: [
        // Case 1: user1 is the invitation creator and user2 is in users array
        {
          invitationBy: user1Id,
          "users.userId": user2Id,
          "users.status": "Accepted",
        },
        // Case 2: user2 is the invitation creator and user1 is in users array
        {
          invitationBy: user2Id,
          "users.userId": user1Id,
          "users.status": "Accepted",
        },
      ],
    })
      .populate("rescheduleBy")
      .populate("feedbackId")
      .populate("invitationBy")
      .populate("businessId")
      .populate("users.userId")
      .sort({ updatedAt: -1 }) // Sort by most recent first
      .limit(1); // Get only the latest one

    if (!latestInvitation) {
      return res
        .status(404)
        .json({ error: "No accepted invitations found between these users" });
    }

    // Fetch the current user's location for distance calculation
    const currentUser = await User.findById(req.user?._id).select("location");
    const currentUserCoords = currentUser?.location?.coordinates || [0, 0]; // Default to [0, 0] if no location

    // Helper function to calculate distance between two coordinates
    const calculateDistance = (userCoords) => {
      const currentUserLatLon = {
        latitude: currentUserCoords[0], // Latitude
        longitude: currentUserCoords[1], // Longitude
      };

      const userLatLon = {
        latitude: userCoords[0] || 0, // Latitude
        longitude: userCoords[1] || 0, // Longitude
      };

      const distance = haversine(currentUserLatLon, userLatLon, {
        unit: "km",
      });

      return parseFloat(distance.toFixed(2)); // Round to 2 decimal places
    };

    // Add distance to each user in the users array
    const usersWithDistance = latestInvitation.users.map((user) => {
      const userCoords = user.userId?.location?.coordinates || [0, 0];
      const distance = calculateDistance(userCoords);

      // Check if toObject method exists and use it, otherwise use the object as is
      const userObj =
        typeof user.toObject === "function" ? user.toObject() : user;
      const userIdObj =
        typeof user.userId?.toObject === "function"
          ? user.userId.toObject()
          : user.userId;

      return {
        ...userObj,
        userId: {
          ...userIdObj,
          distance,
        },
      };
    });

    // Calculate distance for invitation creator
    const invitationByCoords = latestInvitation.invitationBy?.location
      ?.coordinates || [0, 0];
    const invitationByDistance = calculateDistance(invitationByCoords);

    // Create invitationBy user object to add to users array
    const invitationByObj =
      typeof latestInvitation.invitationBy?.toObject === "function"
        ? latestInvitation.invitationBy.toObject()
        : latestInvitation.invitationBy;

    const invitationByUser = {
      userId: {
        ...invitationByObj,
        distance: invitationByDistance,
      },
      status: "Pending",
      _id: latestInvitation.invitationBy._id.toString(),
    };

    // Convert invitation to a plain object and modify it
    const responseData =
      typeof latestInvitation.toObject === "function"
        ? latestInvitation.toObject()
        : latestInvitation;

    if (responseData.isGroup) {
      // If isGroup is true, add the invitationBy user to the users array
      responseData.users = [...usersWithDistance, invitationByUser];
    } else {
      // If isGroup is false, just return the users with distance
      responseData.users = usersWithDistance;
    }

    res.status(200).json({
      data: responseData,
    });
  } catch (err) {
    console.log(err);
    res.status(400).json({ error: "Something Went Wrong" });
  }
};

module.exports.updateInvitation = [
  body("invitationId").notEmpty().withMessage("invitationId is required"),
  body("status")
    .optional()
    .isIn(["Pending", "Accepted", "Rejected"])
    .withMessage("Invalid status"),
  body("reschedule.date")
    .if(body("status").equals("Pending"))
    .isISO8601()
    .withMessage("Valid reschedule date is required"),
  body("reschedule.time")
    .if(body("status").equals("Pending"))
    .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Valid reschedule time (HH:MM) is required"),
  body("deleted")
    .optional()
    .isBoolean()
    .withMessage("Deleted must be a boolean"),
  body("movedToOpenRequests")
    .optional()
    .isBoolean()
    .withMessage("movedToOpenRequests must be a boolean"),
  body("isRemoveOtherInvitation")
    .optional()
    .isBoolean()
    .withMessage("isRemoveOtherInvitation must be a boolean"),

  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const {
        invitationId,
        status,
        reschedule,
        deleted,
        isRemoveOtherInvitation,
      } = req.body;
      const userId = req.user._id;
      console.log("userId", userId);
      // Handle deletion
      if (deleted === true) {
        // First check if invitation exists and user has permission
        const invitationToDelete = await Invitation.findById(invitationId);

        if (!invitationToDelete) {
          return res.status(404).json({ error: "Invitation not found" });
        }

        // Only allow deletion by the invitation creator
        if (!invitationToDelete.invitationBy.equals(userId)) {
          return res.status(403).json({
            error: "Only the invitation creator can delete the invitation",
            case: "UNAUTHORIZED_DELETE",
          });
        }

        // Allow deletion of pending invitations or failed group invitations
        if (
          invitationToDelete.status !== "Pending" &&
          !(
            invitationToDelete.isGroup && invitationToDelete.status === "Failed"
          )
        ) {
          return res.status(400).json({
            error:
              "Only pending invitations or failed group invitations can be deleted",
            case: "INVALID_STATUS_FOR_DELETE",
          });
        }

        const deletedInvitation = await Invitation.findByIdAndUpdate(
          invitationId,
          { deleted: true },
          { new: true }
        );

        // Delete associated chat for group invitations
        if (!!deletedInvitation.isGroup) {
          await Chat.findOneAndUpdate(
            { invitationId: invitationId },
            { deleted: true },
            { new: true }
          );
        }

        // Notify participants
        await Promise.all(
          deletedInvitation.users.map(async (user) => {
            if (user.userId.toString() !== userId.toString()) {
              await UserNotification.create({
                title: req.user.userName,
                body: `${req.user.userName} has withdrawn the invitation`,
                image: req.user.coverImage,
                userId: user.userId,
              });
            }
          })
        );

        return res
          .status(200)
          .json({ message: "Invitation deleted successfully" });
      }

      const invitation = await Invitation.findOne({
        _id: invitationId,
        deleted: { $ne: true },
      }).populate("invitationBy", "userName");

      // Accept rescheduled invitation by sender
      if (!!invitation.isRescheduled && status === "Accepted") {
        console.log(
          "invitation.rescheduleBy.toString()",
          invitation.rescheduleBy.toString()
        );
        const updateObj = {
          $set: {
            "users.$[elem].status": "Accepted",
            status: "Accepted",
          },
        };
        await Invitation.findOneAndUpdate({ _id: invitationId }, updateObj, {
          new: true,
          arrayFilters: [{ "elem.userId": invitation.rescheduleBy.toString() }],
        });
      }
      if (!invitation) {
        return res
          .status(404)
          .json({ error: "Invitation not found or has been withdrawn" });
      }

      // Check authorization (keep existing logic)
      const userInInvitation = invitation.users.find((u) =>
        u.userId.equals(userId)
      );

      if (!userInInvitation && !invitation.invitationBy.equals(userId)) {
        return res.status(403).json({
          error: "You are not authorized to update this invitation",
          case: "UNAUTHORIZED",
        });
      }
      //console.log("userId", userId);
      //console.log(
      //  "invitation.invitationBy.equals(userId)",
      //  invitation.invitationBy.equals(userId)
      //);

      // if (!userInInvitation && !invitation.invitationBy.equals(userId)) {
      //   return res
      //     .status(403)
      //     .json({ error: "Not authorized to update this invitation" });
      // }

      // NEW: Check for conflicting accepted invitations when accepting
      // if (status === "Accepted") {
      //   const conflictCheck = await checkForConflictingAcceptedInvitations(
      //     userId,
      //     invitation
      //   );
      //   if (conflictCheck.hasConflict) {
      //     return res.status(409).json({
      //       error: `Warning: You have already accepted an invitation from ${conflictCheck.conflictingUser} for this time. Accepting this will cancel the previous one.`,
      //       case: "ACCEPTED_CONFLICT",
      //       buttons: ["Accept Anyway", "Cancel"],
      //       conflictingInvitationId: conflictCheck.conflictingInvitationId,
      //     });
      //   }
      // }

      let updateObj = {};
      let notificationStatus;
      let isReschedule = false;

      // Initialize Socket.IO service for status messages and direct MongoDB access
      const socketService = require("../../services/socketService");
      let rescheduleDateTime;
      switch (status) {
        case "Accepted":
          // Handle conflicting invitations within ±3 hours
          const timeParts = parseTimeString(invitation.time);
          let invitationDateTime;

          if (timeParts) {
            invitationDateTime = createUTCDateTime(
              invitation.date.toISOString().split("T")[0],
              `${timeParts[0].toString().padStart(2, "0")}:${timeParts[1]
                .toString()
                .padStart(2, "0")}`
            );

            const dateRange = getDateRangeUTC(invitation.date);
            const startOfDay = dateRange.startOfDay;
            const endOfDay = dateRange.endOfDay;

            // Define the 3-hour window around the accepted invitation
            const threeHoursBefore = subtractTime(
              invitationDateTime,
              3,
              "hours"
            );
            const threeHoursAfter = addTime(invitationDateTime, 3, "hours");

            // Find all pending and accepted invitations for the same day where user is either sender or recipient
            const conflictingInvitations = await Invitation.find({
              $or: [
                // User is the sender
                { invitationBy: userId },
                // User is a recipient
                { "users.userId": userId },
              ],
              date: { $gte: startOfDay, $lt: endOfDay },
              status: ["Pending", "Accepted"],
              deleted: { $ne: true },
              _id: { $ne: invitationId },
            }).populate("invitationBy", "userName");
            console.log("conflictingInvitations", conflictingInvitations);

            // Filter to only include invitations within the 3-hour window
            const conflictsWithin3Hours = [];
            console.log("conflictsWithin3Hours", conflictsWithin3Hours);
            for (const conflictInv of conflictingInvitations) {
              const conflictTimeParts = parseTimeString(conflictInv.time);
              console.log("conflictTimeParts", conflictTimeParts);
              if (conflictTimeParts) {
                const conflictDateTime = createUTCDateTime(
                  conflictInv.date.toISOString().split("T")[0],
                  `${conflictTimeParts[0]
                    .toString()
                    .padStart(2, "0")}:${conflictTimeParts[1]
                    .toString()
                    .padStart(2, "0")}`
                );
                console.log("conflictDateTime", conflictDateTime);
                // Check if the invitation falls within the 3-hour window
                if (
                  conflictDateTime >= threeHoursBefore &&
                  conflictDateTime <= threeHoursAfter
                ) {
                  conflictsWithin3Hours.push({
                    invitation: conflictInv,
                    dateTime: conflictDateTime,
                  });
                }
              }
            }

            // If there are conflicts and user hasn't confirmed removal, return warning
            if (conflictsWithin3Hours.length > 0 && !isRemoveOtherInvitation) {
              // Format conflict information for the response
              const conflictDetails = conflictsWithin3Hours.map((conflict) => {
                const inv = conflict.invitation;
                const dateTime = conflict.dateTime;
                const formattedTime = dateTime.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false,
                });
                const formattedDate = dateTime.toLocaleDateString();

                // Determine if user is sender or recipient
                const isSender =
                  inv.invitationBy.toString() === userId.toString();
                const otherParty = isSender
                  ? inv.users[0]?.userId
                    ? `invitation to ${inv.users[0].userName || "another user"}`
                    : "an invitation"
                  : `invitation from ${
                      inv.invitationBy.userName || "another user"
                    }`;

                return `${otherParty} on ${formattedDate} at ${formattedTime}`;
              });
              console.log("conflictDetails", conflictDetails);
              return res.status(409).json({
                error: `You have ${conflictsWithin3Hours.length} conflicting invitation(s) within ±3 hours of this time. Accepting this invitation will reject these conflicts:`,
                conflictDetails: conflictDetails,
                case: "CONFLICTING_INVITATIONS",
                buttons: ["Accept and Remove Conflicts", "Cancel"],
                conflictCount: conflictsWithin3Hours.length,
              });
            }

            // If user confirmed removal or there are no conflicts, proceed with rejection
            if (isRemoveOtherInvitation && conflictsWithin3Hours.length > 0) {
              // Track rejected invitations for notification
              const rejectedInvitations = [];

              await Promise.all(
                conflictsWithin3Hours.map(async (conflict) => {
                  const conflictInv = conflict.invitation;
                  console.log("conflictInv", conflictInv);
                  // Mark this invitation as rejected
                  await Invitation.findByIdAndUpdate(conflictInv._id, {
                    status: "Rejected",
                    deleted: true,
                    rejectionReason:
                      "Automatically rejected due to conflicting accepted invitation",
                  });

                  rejectedInvitations.push(conflictInv);
                })
              );
              console.log("rejectedInvitations", rejectedInvitations);

              // Send notifications for all rejected invitations
              for (const rejectedInv of rejectedInvitations) {
                // If user is the sender of the rejected invitation, notify all recipients
                if (rejectedInv.invitationBy.toString() === userId.toString()) {
                  await Promise.all(
                    rejectedInv.users.map(async (user) => {
                      await UserNotification.create({
                        title: req.user.userName,
                        body: `${req.user.userName} has canceled the invitation due to a conflicting accepted invitation`,
                        image: req.user.coverImage,
                        userId: user.userId,
                      });
                    })
                  );
                }
                // If user is a recipient of the rejected invitation, notify the sender
                else {
                  await UserNotification.create({
                    title: req.user.userName,
                    body: `${req.user.userName} has rejected your invitation due to a conflicting accepted invitation`,
                    image: req.user.coverImage,
                    userId: rejectedInv.invitationBy,
                  });
                }
              }

              // Log the number of automatically rejected invitations
              console.log(
                `Automatically rejected ${rejectedInvitations.length} conflicting invitations within ±3 hours`
              );
            }
          }

          if (invitation.isGroup) {
            updateObj = {
              $set: {
                "users.$[elem].status": "Accepted",
                "users.$[elem].acceptedAt": now(), // Track when user accepted in UTC
              },
            };
            const updatedInvitation = await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );
            // Check if all users have accepted
            const allAccepted = updatedInvitation.users.every(
              (user) => user.status === "Accepted"
            );

            if (allAccepted) {
              await Invitation.findByIdAndUpdate(invitationId, {
                $set: { status: "Accepted" },
              });
            }
          } else {
            updateObj = {
              $set: {
                "users.$[elem].status": "Accepted",
                "users.$[elem].acceptedAt": now(), // Track when user accepted in UTC
                status: "Accepted",
              },
            };
            await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );
          }

          // updateObj = { status: "Accepted" };
          notificationStatus = "accepted";

          // Send status update message for acceptance
          try {
            console.log("invitation.invitationBy", invitation.invitationBy);
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "accepted",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending acceptance status message:", msgError);
            // Continue with the process even if message sending fails
          }

          // IMPORTANT: Add user to chat participants when they accept invitation
          try {
            const ChatController = require("../Common/ChatController");

            // Create a mock request object for handleInvitationAccepted
            const mockReq = {
              params: { invitationId: invitationId },
              user: {
                _id: userId,
                userName: req.user.userName || req.user.name || "User",
              },
            };

            // Create a mock response object
            const mockRes = {
              status: () => mockRes,
              json: (data) => {
                if (!data.success) {
                  console.error("Error adding user to chat:", data.message);
                }
                return mockRes;
              },
            };
            const chat = await Chat.findOne({ invitationId });
            console.log("chat", chat);
            if (!!chat) {
              // Call handleInvitationAccepted to add user to chat participants
              await ChatController.handleInvitationAccepted(mockReq, mockRes);
              console.log(
                `Successfully added user ${userId} to chat for invitation ${invitationId}`
              );
            }
          } catch (chatError) {
            console.error("Error adding user to chat participants:", chatError);
            // Don't fail the entire request if chat update fails
          }
          break;

        case "Rejected":
          if (invitation.isGroup) {
            updateObj = {
              $set: {
                "users.$[elem].status": "Rejected",
              },
            };

            const updatedInvitation = await Invitation.findOneAndUpdate(
              { _id: invitationId },
              updateObj,
              {
                new: true,
                arrayFilters: [{ "elem.userId": userId }],
              }
            );

            // Check if all users have rejected
            const allRejected = updatedInvitation.users.every(
              (user) => user.status === "Rejected"
            );

            if (allRejected) {
              await Invitation.findByIdAndUpdate(invitationId, {
                $set: { status: "Rejected" },
              });
            }
          } else {
            updateObj = {
              $set: {
                "users.$[elem].status": "Rejected",
                status: "Rejected",
              },
            };
          }
          updateObj = { status: "Rejected" };
          notificationStatus = "rejected";
          console.log("invitation.invitationBy", invitation.invitationBy);
          // Send status update message for rejection
          try {
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "declined",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending rejection status message:", msgError);
            // Continue with the process even if message sending fails
          }
          break;
        case "Pending":
          // Reschedule logic
          if (!reschedule || !reschedule.date || !reschedule.time) {
            return res
              .status(400)
              .json({ error: "Reschedule date and time are required" });
          }

          // Validate reschedule time
          const rescheduleTimeParts = parseTimeString(reschedule.time);
          if (!rescheduleTimeParts) {
            return res
              .status(400)
              .json({ error: "Invalid reschedule time format" });
          }

          // Check if user has already used their reschedule
          if (
            invitation.rescheduleBy &&
            invitation.rescheduleBy.equals(userId)
          ) {
            return res.status(400).json({
              error: "You have already used your one-time reschedule option",
              buttons: ["Accept", "Reject"],
            });
          }

          // Create reschedule datetime in UTC
          rescheduleDateTime = createUTCDateTime(
            reschedule.date,
            reschedule.time
          );

          if (isNaN(rescheduleDateTime.getTime())) {
            return res
              .status(400)
              .json({ error: "Invalid reschedule date/time" });
          }

          // Check for conflicts with the new time - including blocked period conflicts
          const blockedPeriodCheck = await checkForBlockedPeriodConflicts(
            userId,
            rescheduleDateTime
          );

          if (blockedPeriodCheck.hasConflict) {
            const conflictTime =
              blockedPeriodCheck.conflictingDateTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
              });
            const conflictDate =
              blockedPeriodCheck.conflictingDateTime.toLocaleDateString();

            return res.status(409).json({
              error: `You cannot reschedule to this time because it conflicts with your existing invitation with ${blockedPeriodCheck.conflictingUserName} on ${conflictDate} at ${blockedPeriodCheck.conflictingTime}. Please select a time that is at least 3 hours before or after your existing invitation.`,
              case: "RESCHEDULE_BLOCKED_PERIOD_CONFLICT",
              conflictDetails: {
                conflictingUser: blockedPeriodCheck.conflictingUserName,
                conflictingTime: blockedPeriodCheck.conflictingTime,
                conflictingDate: conflictDate,
              },
            });
          }

          const dateRange = getDateRangeUTC(reschedule.date);
          const startOfDay = dateRange.startOfDay;
          const endOfDay = dateRange.endOfDay;

          // Check for active invitations only (exclude completed ones with feedback)
          const userInvitations = await Invitation.find({
            $or: [
              {
                invitationBy: userId,
                date: { $gte: startOfDay, $lt: endOfDay },
              },
              {
                "users.userId": userId,
                date: { $gte: startOfDay, $lt: endOfDay },
              },
            ],
            status: { $in: ["Pending", "Accepted"] },
            deleted: { $ne: true },
            _id: { $ne: invitationId },
          }).populate("feedbackId");

          // Filter out completed invitations
          const activeUserInvitations = userInvitations.filter((inv) => {
            return !inv.feedbackId || !inv.feedbackId.given;
          });

          for (const inv of activeUserInvitations) {
            const invTimeParts = parseTimeString(inv.time);
            if (invTimeParts) {
              const invDateTime = createUTCDateTime(
                inv.date.toISOString().split("T")[0],
                `${invTimeParts[0]
                  .toString()
                  .padStart(2, "0")}:${invTimeParts[1]
                  .toString()
                  .padStart(2, "0")}`
              );

              const diffInHours = getTimeDifference(
                rescheduleDateTime,
                invDateTime,
                "hours"
              );

              // Check for exact time conflicts (within 1 hour) and 3-hour block conflicts
              if (diffInHours < 1) {
                // Exact time conflict
                const conflictDate = formatForAPI(invDateTime).split("T")[0];

                return res.status(409).json({
                  error: `You already have another invitation scheduled at this exact time (${conflictDate} at ${inv.time}). Please choose a different time.`,
                  case: "RESCHEDULE_EXACT_CONFLICT",
                });
              } else if (diffInHours < 3) {
                // 3-hour block conflict
                const conflictDate = formatForAPI(invDateTime).split("T")[0];
                const hoursDiff = Math.round(diffInHours * 10) / 10; // Round to 1 decimal

                return res.status(409).json({
                  error: `You have another invitation within 3 hours of this time (${conflictDate} at ${inv.time}, ${hoursDiff} hours away). Please select a time that is at least 3 hours before or after your existing invitation.`,
                  case: "RESCHEDULE_BLOCK_CONFLICT",
                });
              }
            }
          }

          updateObj = {
            $set: {
              isRescheduled: true,
              rescheduleBy: userId,
              reschedule: {
                date: reschedule.date,
                time: reschedule.time,
              },
              date: rescheduleDateTime,
              time: reschedule.time,
              status: "Pending",
            },
            $setOnInsert: {
              "users.$[].status": "Pending",
            },
          };
          // ENHANCED RESCHEDULE LOGIC
          const rescheduleCount = await getRescheduleCount(invitation);
          const isSender = invitation.invitationBy.equals(userId);

          // Check reschedule limits
          if (rescheduleCount >= 2) {
            return res.status(400).json({
              error: "Maximum reschedules reached for this invitation",
              buttons: ["Accept", "Reject"],
            });
          }

          // Check if this user can reschedule
          if (rescheduleCount === 1 && isSender) {
            // Second reschedule by sender - move to open requests
            updateObj = {
              $set: {
                isRescheduled: true,
                rescheduleBy: userId,
                rescheduleCount: 2,
                reschedule: {
                  date: reschedule.date,
                  time: reschedule.time,
                },
                date: rescheduleDateTime,
                time: reschedule.time,
                status: "Pending",
                movedToOpenRequests: true, // Flag for frontend
              },
              $setOnInsert: {
                "users.$[].status": "Pending",
              },
            };
          } else if (
            rescheduleCount === 0 ||
            (rescheduleCount === 1 && !isSender)
          ) {
            // First reschedule or second by receiver
            updateObj = {
              $set: {
                isRescheduled: true,
                rescheduleBy: userId,
                rescheduleCount: rescheduleCount + 1,
                reschedule: {
                  date: reschedule.date,
                  time: reschedule.time,
                },
                date: rescheduleDateTime,
                time: reschedule.time,
                status: "Pending",
              },
              $setOnInsert: {
                "users.$[].status": "Pending",
              },
            };
          }

          notificationStatus = "Rescheduled";
          isReschedule = true;
          console.log("invitation.invitationBy", invitation.invitationBy);
          // Send status update message for rescheduling
          try {
            await socketService.sendInvitationResponseMessages(
              invitationId,
              "rescheduled",
              userId.toString(),
              invitation.invitationBy,
              invitation.isGroup || false
            );
          } catch (msgError) {
            console.error("Error sending reschedule status message:", msgError);
            // Continue with the process even if message sending fails
          }
          break;
      }

      // Apply updates
      const updatedInvitation = await Invitation.findOneAndUpdate(
        { _id: invitationId },
        updateObj,
        {
          new: true,
          arrayFilters: [{ "elem.userId": userId }],
        }
      ).populate("users.userId invitationBy");

      if (!updatedInvitation) {
        return res.status(404).json({ error: "Failed to update invitation" });
      }

      // Create notification
      let notificationMessage = "";
      const isGroup = updatedInvitation.isGroup;
      const invitationOwner = updatedInvitation.invitationBy;

      if (notificationStatus === "Accepted") {
        notificationMessage = `${req.user.userName} has accepted ${
          isGroup ? "the group invitation" : "your invitation"
        }`;
      } else if (notificationStatus === "Rejected") {
        notificationMessage = `${req.user.userName} has rejected ${
          isGroup ? "the group invitation" : "your invitation"
        }`;
      } else if (notificationStatus === "Rescheduled") {
        const date = rescheduleDateTime;
        const formattedDate = date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
        notificationMessage = `${req.user.userName} has rescheduled ${
          isGroup ? "the group invitation" : "your invitation"
        } to ${formattedDate} at ${reschedule.time}`;
      }

      // Notify invitation owner
      await UserNotification.create({
        title: req.user.userName,
        body: notificationMessage,
        image: req.user.coverImage,
        userId: invitationOwner._id,
      });

      // Send email to owner
      await sendEmail({
        from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
        to: invitationOwner.email,
        subject: "Invitation Update Notification",
        text: `Hey,\n${notificationMessage}`,
      });

      // For group invitations, notify all members about reschedule
      if (isReschedule && isGroup) {
        await Promise.all(
          updatedInvitation.users.map(async (user) => {
            if (!user.userId.equals(userId)) {
              await UserNotification.create({
                title: req.user.userName,
                body: notificationMessage,
                image: req.user.coverImage,
                userId: user.userId,
              });

              const userDoc = await User.findById(user.userId);
              await sendEmail({
                from: `"NETME App" <${process.env.USER_APP_EMAIL}>`,
                to: userDoc.email,
                subject: "Group Invitation Update",
                text: `Hey,\n${notificationMessage}`,
              });
            }
          })
        );
      }

      res.status(200).json({ invitation: updatedInvitation });
    } catch (err) {
      console.error("Error in updateInvitation:", err);
      res.status(500).json({
        error: "Failed to update invitation",
        details:
          process.env.NODE_ENV === "development" ? err.message : undefined,
      });
    }
  },
];

// parseTimeOfDay function removed - replaced with parseTimeString from timeUtils

/**
 * Check if a new invitation time conflicts with the blocked period of any active invitation
 * Blocked period is defined as 3 hours before and 3 hours after the existing invitation time
 * @param {String} userId - User ID to check for conflicts
 * @param {Date} newDateTime - The proposed new invitation datetime
 * @returns {Promise<Object>} - Object with conflict information
 */
async function checkForBlockedPeriodConflicts(
  userId,
  newDateTime,
  targetUserId = null
) {
  // Only consider current and future invitations
  const now = new Date();

  // Get all active invitations (Accepted or Pending) for this user
  const activeInvitations = await Invitation.find({
    $or: [
      // User is the inviter
      { invitationBy: userId },
      // User is an invitee
      { "users.userId": userId },
    ],
    status: { $in: ["Accepted", "Pending"] },
    deleted: { $ne: true },
    // Only consider current and future invitations
    date: { $gte: now },
  })
    .populate("invitationBy", "userName")
    .populate("feedbackId");

  // Filter out completed invitations (those with feedback given)
  const trulyActiveInvitations = activeInvitations.filter((inv) => {
    return !inv.feedbackId || !inv.feedbackId.given;
  });

  for (const invitation of trulyActiveInvitations) {
    const timeParts = extractTimeParts(invitation.time);
    if (!timeParts) continue;
    console.log("timeParts", timeParts);
    console.log("invitation.date", invitation.date);

    const blockedStart = new Date(invitation.date);
    blockedStart.setHours(invitation.date.getHours() - 3);

    const blockedEnd = new Date(invitation.date);
    blockedEnd.setHours(invitation.date.getHours() + 3);

    // If the blocked period has already ended (more than 3 hours after the meeting), allow new invitations
    const now = new Date();
    if (blockedEnd < now) {
      continue; // Skip this invitation as its blocked period has passed
    }

    // Check if new datetime falls within the blocked period
    if (newDateTime >= blockedStart && newDateTime <= blockedEnd) {
      // Check if this is an existing invitation between the same users (for 1-on-1 only)
      if (targetUserId && !invitation.isGroup) {
        const isExistingInvitationBetweenUsers =
          (invitation.invitationBy._id.toString() === userId.toString() &&
            invitation.users.some(
              (user) => user.userId.toString() === targetUserId.toString()
            )) ||
          (invitation.invitationBy._id.toString() === targetUserId.toString() &&
            invitation.users.some(
              (user) => user.userId.toString() === userId.toString()
            ));

        if (isExistingInvitationBetweenUsers) {
          // Get the other user's name
          let conflictingUserName = "";
          if (invitation.invitationBy._id.toString() === userId.toString()) {
            const inviteeUser = await User.findById(targetUserId).select(
              "userName"
            );
            conflictingUserName = inviteeUser
              ? inviteeUser.userName
              : "another user";
          } else {
            conflictingUserName =
              invitation.invitationBy.userName || "another user";
          }

          const isCurrentUserSender =
            invitation.invitationBy._id.toString() === userId.toString();
          const errorMessage = isCurrentUserSender
            ? `You already have an active invitation with ${conflictingUserName}. Please wait for them to respond or cancel the existing invitation.`
            : `${conflictingUserName} has already sent you an invitation. Please respond to their invitation first.`;

          return {
            hasConflict: true,
            conflictType: "EXISTING_INVITATION_BETWEEN_USERS",
            errorMessage,
            existingInvitationId: invitation._id,
            conflictingUserName,
          };
        }
      }

      // If not an existing invitation between users, return blocked period conflict
      // Get the other user's name for better error messaging
      let conflictingUserName = "";

      if (invitation.invitationBy._id.toString() === userId.toString()) {
        // Current user is the inviter, so get the first invitee's name
        if (invitation.users && invitation.users.length > 0) {
          const inviteeUser = await User.findById(
            invitation.users[0].userId
          ).select("userName");
          conflictingUserName = inviteeUser
            ? inviteeUser.userName
            : "another user";
        }
      } else {
        // Current user is an invitee, so get the inviter's name
        conflictingUserName =
          invitation.invitationBy.userName || "another user";
      }

      return {
        hasConflict: true,
        conflictType: "BLOCKED_PERIOD_CONFLICT",
        conflictingInvitationId: invitation._id,
        conflictingUserName,
        conflictingDateTime: invitation.date,
        conflictingTime: invitation.time,
        isWithinBlockedPeriod: true,
      };
    }
  }

  return { hasConflict: false };
}
// async function checkForBlockedPeriodConflicts(userId, newDateTime) {
//   // Only consider current and future invitations
//   const now = new Date();

//   // Get all active invitations (Accepted or Pending) for this user
//   const activeInvitations = await Invitation.find({
//     $or: [
//       // User is the inviter
//       { invitationBy: userId },
//       // User is an invitee
//       { "users.userId": userId },
//     ],
//     status: { $in: ["Accepted", "Pending"] },
//     deleted: { $ne: true },
//     // Only consider current and future invitations
//     date: { $gte: now },
//   })
//     .populate("invitationBy", "userName")
//     .populate("feedbackId");

//   // Filter out completed invitations (those with feedback given)
//   const trulyActiveInvitations = activeInvitations.filter((inv) => {
//     return !inv.feedbackId || !inv.feedbackId.given;
//   });

//   for (const invitation of trulyActiveInvitations) {
//     const timeParts = extractTimeParts(invitation.time);
//     if (!timeParts) continue;
//     console.log("timeParts", timeParts);
//     console.log("invitation.date", invitation.date);

//     const blockedStart = new Date(invitation.date);
//     blockedStart.setHours(invitation.date.getHours() - 3);

//     const blockedEnd = new Date(invitation.date);
//     blockedEnd.setHours(invitation.date.getHours() + 3);

//     // If the blocked period has already ended (more than 3 hours after the meeting), allow new invitations
//     const now = new Date();
//     if (blockedEnd < now) {
//       continue; // Skip this invitation as its blocked period has passed
//     }

//     // Check if new datetime falls within the blocked period
//     if (newDateTime >= blockedStart && newDateTime <= blockedEnd) {
//       // Get the other user's name for better error messaging
//       let conflictingUserName = "";

//       if (invitation.invitationBy._id.toString() === userId.toString()) {
//         // Current user is the inviter, so get the first invitee's name
//         if (invitation.users && invitation.users.length > 0) {
//           const inviteeUser = await User.findById(
//             invitation.users[0].userId
//           ).select("userName");
//           conflictingUserName = inviteeUser
//             ? inviteeUser.userName
//             : "another user";
//         }
//       } else {
//         // Current user is an invitee, so get the inviter's name
//         conflictingUserName =
//           invitation.invitationBy.userName || "another user";
//       }

//       return {
//         hasConflict: true,
//         conflictingInvitationId: invitation._id,
//         conflictingUserName,
//         conflictingDateTime: invitation.date,
//         conflictingTime: invitation.time,
//         isWithinBlockedPeriod: true,
//       };
//     }
//   }

//   return { hasConflict: false };
// }

async function getRescheduleCount(invitation) {
  // Count how many times this invitation has been rescheduled
  if (!invitation.isRescheduled) return 0;

  // Check if we have a stored count
  if (invitation.rescheduleCount) return invitation.rescheduleCount;

  // For backwards compatibility with existing invitations
  return invitation.rescheduleBy ? 1 : 0;
}
function extractTimeParts(timeString) {
  // First try the TimeOfDay(HH:MM) format
  const timeRegex = /TimeOfDay\((\d+):(\d+)\)/; // Format like 'TimeOfDay(22:00)'
  const match = timeString.match(timeRegex);

  if (match && match.length === 3) {
    return [parseInt(match[1]), parseInt(match[2])];
  }

  // If that fails, try the simple HH:MM format
  const simpleTimeRegex = /^(\d+):(\d+)$/; // Format like '13:55'
  const simpleMatch = timeString.match(simpleTimeRegex);

  if (simpleMatch && simpleMatch.length === 3) {
    return [parseInt(simpleMatch[1]), parseInt(simpleMatch[2])];
  }

  // If both formats fail, return null
  return null;
}
